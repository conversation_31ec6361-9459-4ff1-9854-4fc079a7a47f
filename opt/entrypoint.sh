#!/bin/sh
set -e

cd /var/www/html

echo "Environment: ${APP_ENV}"
if [ "${APP_ENV}" = "local" ]
then
	echo "Running composer and npm install in local".
	composer install
fi


# We need this coz' environment variables are not present on build time
envsubst < .env.example > .env
php artisan migrate --force --no-interaction --database

if [ "${APP_ENV}" = "production" ]
then
	echo "Caching configurations".
	php artisan route:cache
	php artisan view:cache
fi

exec "$@"
