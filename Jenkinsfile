node("master") {
    stage("Set agent") {
        if (params.ENVIRONMENT != 'prod') {
            AGENT_LABEL = "piwi-ec2-fleet"
        } else {
            AGENT_LABEL = "master"
        }
        print "Agent running: ${AGENT_LABEL}"
    }
}

pipeline {
    agent {
        label "${AGENT_LABEL}"
    }
    parameters {
        choice(
            name: 'ENVIRONMENT',
            choices: ['stage1', 'stage2', 'stage3', 'stage4', 'prod'],
            description: 'Environment to deploy to'
        )
        string(
            name: 'BRANCH',
            defaultValue: 'master',
            description: 'Branch to be deployed',
            trim: true
        )
        booleanParam(
            name: 'UPDATE_DOCKER_COMPOSE_FILE',
            defaultValue: false,
            description: 'Update Update Content of Docker Compose File'
        )
    }
    environment {
        GIT_URL = "*****************:zimitech/affiliate-service.git"
        SERVICE_PHP = "affiliate-service-php"
        SERVICE_WEB = "affiliate-service-web"
        SERVICE_NAME = "affiliate-service"
        ECR_REGISTRY_PHP = "025440791034.dkr.ecr.ap-northeast-1.amazonaws.com/microservice/affiliate-service-php"
        ECR_REGISTRY_WEB = "025440791034.dkr.ecr.ap-northeast-1.amazonaws.com/microservice/affiliate-service-web"
        AWS_REGION = "ap-northeast-1"
        AWS_S3_BUCKET = "zmt-piwi-build-artifacts"
        AWS_S3_CONF = "s3://${AWS_S3_BUCKET}/docker/affiliate"
        AWS_S3_CONF_BAK = "s3://${AWS_S3_BUCKET}/backup/affiliate"
        DOCKER_BUILD_PATH = "src"
        FILE_CONF = "docker-compose.yml"
        FILE_CONF_BAK = "docker-compose-${JOB_NAME}-${BUILD_ID}.yml"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
    }
    stages {
        stage("Update Job Description") {
            steps {
                script {
                    def cause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                    def user = null
                    if (cause != null && cause.size() > 0) {
                        user = cause[0].userName
                    } else {
                        cause = cause = currentBuild.getBuildCauses('org.jenkinsci.plugins.workflow.support.steps.build.BuildUpstreamCause')
                        if (cause != null && cause.size() > 0) {
                            def project = cause[0].upstreamProject
                            def build = cause[0].upstreamBuild
                            user = "<a href='/job/${project}/${build}/'>${project}/${build}</a>"
                        }
                    }
                    currentBuild.description = "ENV: ${params.ENVIRONMENT}<br/>BRANCH: ${BRANCH}<br/>Run by: ${user}<br/><img src='${JENKINS_URL}/buildStatus/icon?job=${JOB_NAME}&style=flat&build=${BUILD_ID}'>"
                }
            }
        }
        stage('Install Dependencies') {
            steps {
                script {
                    try {
                        //if agent used is master, change below command to sudo apt-get install jq -y
                        if(env.NODE_NAME == "master") {
                            sh "sudo apt-get install jq -y"
                        } else {
                            sh "sudo yum install jq -y"
                        }
                    } catch(Exception e) {
                        sh "echo \"Something wrong with the installation.\""
                        sh "echo \"Exception: ${e}\""
                    }
                }
            }
        }
        stage("Request permission to proceed on PROD") {
            when {
                expression {
                    params.ENVIRONMENT == "prod"
                }
            }
            steps {
                script {
                    def cause = cause = currentBuild.getBuildCauses('org.jenkinsci.plugins.workflow.support.steps.build.BuildUpstreamCause')
                    def userCause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                    def user = null
                    if (userCause != null && userCause.size() > 0) {
                        user = userCause[0].userId
                    }
                    if (params.ENVIRONMENT == 'prod' && (cause == null || cause.size() == 0)) {
                        while ({
                                def approver = input submitterParameter: 'approver',
                                message: 'You are deploying to PRODUCTION, seek approval.'
                                env.approver = approver
                                env.approver == user
                            }()) continue
                    }
                    if (env.approver != null && env.approver != '') {
                        user = "${user}<br/>Approver: ${env.approver}"
                    }
                    currentBuild.description = "ENV: ${params.ENVIRONMENT}<br/>BRANCH: ${BRANCH}<br/>Run by: ${user}<br/><img src='${JENKINS_URL}/buildStatus/icon?job=${JOB_NAME}&style=flat&build=${BUILD_ID}'>"
                }
            }
        }
        stage("Retrieve docker-compose files") {
            steps {
                script {
                    sh "aws s3 cp --only-show-errors  ${AWS_S3_CONF}/${params.ENVIRONMENT}/${FILE_CONF} ${WORKSPACE}/${BUILD_ID}/${FILE_CONF}"
                    dir("${WORKSPACE}/${BUILD_ID}") {
                        stash "${JOB_NAME}-${BUILD_ID}"
                    }
                }
            }
        }
        stage("Clone, Merge, and Build") {
            steps {
                lock(resource: "${JOB_NAME}_src") {
                    // clone
                    script {
                        isTag = ""
                        try {
                            sh "git clone ${GIT_URL} ${DOCKER_BUILD_PATH}"
                        } catch (exception) {
                            sh "cd src && git reset --hard -q && git clean -fdX && git checkout master && git pull -q"
                        }

                        // verify if tag or branch
                        dir("${DOCKER_BUILD_PATH}") {
                            isTag = sh(
                                returnStdout: true,
                                script: "git tag -l ${BRANCH}"
                            ).trim()

                            if (isTag == "") {
                                // merge without commit if branch
                                sh "git merge --stat --no-ff --no-commit origin/${BRANCH}"
                            } else {
                                // if tag, checkout
                                sh "git checkout tags/${BRANCH}"
                            }

                            // build
                            def login = ecrLogin()
                            sh "${login}"
                            if (params.ENVIRONMENT != "prod") {
                                sh "docker build --force-rm --pull --memory 4g --memory-swap -1 --shm-size 512m -t ${ECR_REGISTRY_PHP}:${BRANCH} --compress ."
                                sh "docker build -f nginx.dockerfile --force-rm --pull --memory 4g --memory-swap -1 --shm-size 512m -t ${ECR_REGISTRY_WEB}:${BRANCH} --compress ."
                            } else {
                                sh "echo 'No docker build for prod'"
                            }
                        }
                    }
                }
            }
        }
        stage("Update Docker Compose Values") {
            when {
                expression {
                    (params.UPDATE_DOCKER_COMPOSE_FILE == true && params.ENVIRONMENT != "prod") || (params.UPDATE_DOCKER_COMPOSE_FILE == true && params.ENVIRONMENT == "prod" && env.approver != null && env.approver != '') //will execute stage env or prod that has an approval 
                }
            }
            steps {
                script {
                    dir("${WORKSPACE}/${BUILD_ID}") {
                        unstash "${JOB_NAME}-${BUILD_ID}"

                        def cause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                        def user = null
                        if (cause != null && cause.size() > 0) {
                            user = cause[0].userName
                        }

                        MESSAGE = "Provide Complete Updated Values"

                        def contents = readFile("${FILE_CONF}")
                        env.NEWVALUE = input message: "${MESSAGE}", parameters: [text(defaultValue: "${contents}", description: '', name: 'NEWVALUE')]
                        writeFile file: "${FILE_CONF}", text: "${env.NEWVALUE}"

                        stash "${JOB_NAME}-${BUILD_ID}"
                    }
                }
            }
        }
        stage("Post Build") {
            when {
                expression {
                    params.ENVIRONMENT != "prod"
                }
            }
            parallel {
                stage("Push to Amazon ECR") {
                    steps {
                        withAWS(region: "${AWS_REGION}") {
                            script {
                                def login = ecrLogin()
                                sh "${login}"
                                sh "docker push ${ECR_REGISTRY_PHP}:${BRANCH}"
                                sh "docker push ${ECR_REGISTRY_WEB}:${BRANCH}"
                            }
                        }
                    }
                }
                stage("Check Production and Amazon ECR Push") {
                    steps {
                        withAWS(region: "${AWS_REGION}") {
                            script {
                                if (isTag.trim() != "") {
                                    def login = ecrLogin()
                                    sh "${login}"
                                    sh "docker tag ${ECR_REGISTRY_PHP}:${BRANCH} ${ECR_REGISTRY_PHP}:latest"
                                    sh "docker push ${ECR_REGISTRY_PHP}:latest"
                                    sh "docker tag ${ECR_REGISTRY_WEB}:${BRANCH} ${ECR_REGISTRY_WEB}:latest"
                                    sh "docker push ${ECR_REGISTRY_WEB}:latest"
                                }
                            }
                        }
                    }
                }
            }
        }
        stage("Check docker tag If exists") {
            steps {
                script {
                    if (params.ENVIRONMENT == "prod") {
                        def login = ecrLogin()
                        sh "${login}"
                        def checkTag = sh(
                            script: "docker pull ${ECR_REGISTRY_PHP}:${BRANCH} || true",
                            returnStdout: true
                        ).trim() as Boolean

                        if (!checkTag) {
                            echo "Build the tag before deploying on production."
                            currentBuild.result = "FAILED"
                            error("Aborting build")
                        }
                    }
                }
            }
        }
        stage("Update docker-compose files") {
            environment {
                IMAGE_TAG = "${BRANCH}"
            }
            steps {
                dir("${WORKSPACE}/${BUILD_ID}") {
                    unstash "${JOB_NAME}-${BUILD_ID}"
                    sh 'sed -i "s/\\(.*\\/${SERVICE_PHP}\\):\\(.*\\)/\\1:${IMAGE_TAG}/g" ${FILE_CONF}'
                    sh 'sed -i "s/\\(.*\\/${SERVICE_WEB}\\):\\(.*\\)/\\1:${IMAGE_TAG}/g" ${FILE_CONF}'
                    sh "cp ${FILE_CONF} ${FILE_CONF_BAK}"
                }
            }
        }
        stage("Upload new docker-compose files") {
            steps {
                script {
                    dir("${WORKSPACE}/${BUILD_ID}") {
                        sh "aws s3 cp --only-show-errors ${FILE_CONF_BAK} ${AWS_S3_CONF}/${params.ENVIRONMENT}/${FILE_CONF}"
                        sh "aws s3 cp --only-show-errors ${FILE_CONF_BAK} ${AWS_S3_CONF_BAK}/${params.ENVIRONMENT}/${FILE_CONF_BAK}"
                    }
                }
            }
        }
        stage("Deploy new docker-compose files") {
            steps {
                script {
                    if(params.ENVIRONMENT == "stage1" || params.ENVIRONMENT == "stage3") {
                        EC2_TAG = "Stage-Odd"
                    } else if(params.ENVIRONMENT == "stage2" || params.ENVIRONMENT == "stage4") {
                        EC2_TAG = "Stage-Even"
                    } else if(params.ENVIRONMENT == "prod"){
                        EC2_TAG = "Prod"
                    } else {
                        error "Environment not found to deploy docker compose files. Please check.";
                    }
                    
                    def PREFIX = params.ENVIRONMENT == "prod" ? "prod" : "test"
                    def RUN_COMMAND_ID
                    def RUN_COMMAND_STATUS
                    def ATTEMPTS = 0
                    def MAX_ATTEMPTS = 10 //max of 10mins: DELAY * MAX_ATTEMPTS
                    def DELAY = 60 // seconds

                    // Send the command and get the command ID
                    def RUN_COMMAND_RESULT = sh(
                        script: """ \
                            aws ssm send-command --profile piwi-prod --document-name \"AWS-RunShellScript\" \
                                --targets '{\"Key\":\"tag:ENVIRONMENT\",\"Values\":[\"${EC2_TAG}\"]}' \
                                --max-concurrency \"1\" \
                                --parameters '{ \
                                    \"commands\": [ \
                                        \"aws s3 cp ${AWS_S3_CONF}/${params.ENVIRONMENT}/${FILE_CONF} ${params.ENVIRONMENT}/${JOB_NAME}/${FILE_CONF} \
                                        && aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin 025440791034.dkr.ecr.${AWS_REGION}.amazonaws.com \
                                        && docker-compose -f ${params.ENVIRONMENT}/${JOB_NAME}/${FILE_CONF} pull \
                                        && docker-compose -f ${params.ENVIRONMENT}/${JOB_NAME}/${FILE_CONF} -p ${PREFIX} up -d --always-recreate-deps --force-recreate\" \
                                    ], \
                                    \"executionTimeout\":[\"3600\"], \
                                    \"workingDirectory\":[\"/home/<USER>"] \
                                    }' \
                                --comment ${ENVIRONMENT}-${SERVICE_NAME}-${BRANCH} \
                                --timeout-seconds 4200 \
                                --region ${AWS_REGION}
                        """,
                        returnStdout: true
                    ).trim()

                    // Extract command ID from the send-command result
                    RUN_COMMAND_ID = sh(script: "echo '${RUN_COMMAND_RESULT}' | jq -r '.Command.CommandId'", returnStdout: true).trim()

                    while (ATTEMPTS < MAX_ATTEMPTS) {
                        // Get the execution status of the command
                        RUN_COMMAND_STATUS = sh(
                            script: """ \
                                aws --profile piwi-prod --region ${AWS_REGION} ssm list-commands \
                                --command-id ${RUN_COMMAND_ID} --query 'Commands[0].Status' --output text
                            """,
                            returnStdout: true
                        ).trim()

                        // Check the execution status
                        if(RUN_COMMAND_STATUS == "Success") {
                            echo "Deployment in EC2 instance(s) via SSM was successful."
                            break
                        } else if(RUN_COMMAND_STATUS == "InProgress" || RUN_COMMAND_STATUS == "Pending") {
                            echo "Deployment in EC2 instance(s) via SSM is still in progress..."
                            ATTEMPTS++
                            sleep DELAY
                        } else if(RUN_COMMAND_STATUS == "Failed" || RUN_COMMAND_STATUS == "Terminated") {
                            error "Something went wrong of the deployment in EC2 instance(s) via SSM. You may check this link for more information: https://ap-northeast-1.console.aws.amazon.com/systems-manager/run-command/${RUN_COMMAND_ID}?region=ap-northeast-1 or you may ask assistance from DevOps to further check the error."
                            break
                        } else {
                            echo "Unknown status of the deployment in EC2 instance(s) via SSM. You may check this link for more information: https://ap-northeast-1.console.aws.amazon.com/systems-manager/run-command/${RUN_COMMAND_ID}?region=ap-northeast-1 or you may ask assistance from DevOps to further check the error."
                            break
                        }
                    }

                    if (ATTEMPTS == MAX_ATTEMPTS) {
                        // Check the execution status
                        switch(RUN_COMMAND_STATUS) {
                            case "Success":
                                echo "Deployment via SSM run command was successful."
                                break;
                            case ["Failed", "Terminated"]:
                                error "Something went wrong of the deployment in EC2 instance(s) via SSM. You may check this link for more information: https://ap-northeast-1.console.aws.amazon.com/systems-manager/run-command/${RUN_COMMAND_ID}?region=ap-northeast-1 or you may ask assistance from DevOps to further check the error."
                                break;
                            default:
                                echo "Maximum number of attempts reached. Deployment status in EC2 instance(s) via SSM is still UNKNOWN after max attempts. You may check this link for more information: https://ap-northeast-1.console.aws.amazon.com/systems-manager/run-command/${RUN_COMMAND_ID}?region=ap-northeast-1 or you may ask assistance from DevOps to further check the error."
                                break;
                        }
                    }
                }
            }
        }
    }
    post {
        failure {
            script {
                echo "This Job has failed."
            }
        }
        success {
            script {
                echo "This Job has been successful"
            }
        }
        always {
            script {
                def cause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                def user = null
                if (cause != null && cause.size() > 0) {
                    user = cause[0].userName
                } else {
                    cause = cause = currentBuild.getBuildCauses('org.jenkinsci.plugins.workflow.support.steps.build.BuildUpstreamCause')
                    if (cause != null && cause.size() > 0) {
                        def project = cause[0].upstreamProject
                        def build = cause[0].upstreamBuild
                        user = "<${JENKINS_URL}/job/${project}/${build}/${project}/${build}|Open>"
                    }
                }
                if (env.approver != null && env.approver != '') {
                    user = "${user}\nApprover: ${env.approver}"
                }
                currentBuild.description = "ENV: ${params.ENVIRONMENT}<br/>BRANCH: ${BRANCH}<br/>Run by: ${user}<br/><img src='${JENKINS_URL}/buildStatus/icon?job=${JOB_NAME}&style=flat&build=${BUILD_ID}'>"
            }
        }
    }
}