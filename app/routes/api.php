<?php

use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\AffiliateMemberMappingController;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\ContactUsController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/ 

Route::middleware(['cache.headers:public;max_age=0;must_revalidate;etag'])->prefix('banner')->group(function () {
    Route::get('', [BannerController::class, 'index']);
    Route::post('', [BannerController::class, 'store']);
    Route::get('/{banner}', [BannerController::class, 'show']);
    Route::delete('/{banner}', [BannerController::class, 'delete']);
    Route::post('/{banner}', [BannerController::class, 'update'])->name('banner.update');
    Route::get('/{banner}/download', [BannerController::class, 'download'])->name('banner.download');
    Route::get('/{banner}/images', [BannerController::class, 'images']);
    Route::delete('/{banner}/images/{size}', [BannerController::class, 'deleteImage']);
});

Route::middleware(['cache.headers:public;max_age=0;must_revalidate;etag'])->prefix('affiliate')->group(function () {
    Route::get('', [AffiliateController::class, 'index']);
    Route::post('', [AffiliateController::class, 'store']);
    Route::get('/by-referral-code/{referralCode}', [AffiliateController::class, 'byReferralCode']);
    Route::post('/link-member', [AffiliateController::class, 'linkMember']);
    Route::get('/member-affiliate/{memberUserId}', [AffiliateController::class, 'memberAffiliate']);
    Route::get('/search-members', [AffiliateController::class, 'searchMembers']);
    Route::get('/{userId}', [AffiliateController::class, 'show']);
    Route::post('/{userId}/update-profile', [AffiliateController::class, 'updateProfile']);
    Route::post('/{userId}/member', [AffiliateController::class, 'storeMember']);
    Route::get('/{userId}/member', [AffiliateController::class, 'listMembers']);
    Route::delete('/{userId}/member/{memberUserId}', [AffiliateController::class, 'removeMember']);
    Route::post('/{userId}/referral-code', [AffiliateController::class, 'addReferralCode']);
    Route::get('/{userId}/referral-code', [AffiliateController::class, 'listReferralCodes']);
    Route::post('/{userId}/banner', [AffiliateController::class, 'addBanner']);
    Route::delete('/{userId}/banner/{referralCode}', [AffiliateController::class, 'removeBanner']);
    Route::get('/{userId}/banner', [AffiliateController::class, 'listBanners']);
    Route::post('/contact-us', [ContactUsController::class, 'sendMessage'])->name('contact-us');
});