<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model; 
use Illuminate\Support\Facades\Storage;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'type', 'language'];
    protected $appends = ['sizes'];
    protected $casts = [
        'paths' => 'array',
    ];

    protected $attributes = [
        'paths' => '[]',
    ];

    public function scopeType($query, $type)
    {
        if (trim($type ?? '') !== '') {
            $query->where('type', trim($type));
        }
    }

    public function scopeLanguage($query, $language)
    {
        if (trim($language ?? '') !== '') {
            $query->where('language', trim($language));
        }
    }

    public function scopeName($query, $name)
    {
        if (trim($name ?? '') !== '') {
            $query->where('name', 'LIKE', trim($name) . '%');
        }
    }

    public function addToPaths(array $newPaths)
    {
        $paths = $this->paths;

        foreach ($newPaths as $path) {
            $paths[$path['size']] = $path['path'];
        }
        
        $this->paths = $paths;

        return $paths;
    }

    public function removeFromPaths(string $size)
    {
        $paths = collect($this->paths);
        $filtered = $paths->filter(function ($item, $key) use ($size){
            return $size !== $key;
            })
             ->toArray()
        ;
        $this->paths = $filtered;
    }

    public function getSizesAttribute()
    {
        return join(',', array_keys($this->paths));
    }
}
