<?php

namespace App\Models;

use App\Events\AffiliateMemberMappingSaved;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class AffiliateMemberMapping extends Model
{
    use HasFactory;

    protected $fillable = [
        'referral_code',
        'affiliate_user_id',
        'member_user_id'
    ];
    protected $appends = ['member_details'];

    protected $dispatchesEvents = [
        'saved' => AffiliateMemberMappingSaved::class,
    ];

    public function getMemberDetailsAttribute()
    {
        return  DB::table('user')
            ->where('user_id', '=', $this->member_user_id)
            ->select('user_username', 'user_email', 'customer_full_name')
            ->join('customer', 'customer.customer_user_id', '=', 'user.user_id')
            ->get()
            ->first()
        ;
    }
}
