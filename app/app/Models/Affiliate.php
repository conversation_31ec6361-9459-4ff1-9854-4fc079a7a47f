<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Affiliate extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'name', 'email', 'username'];
    protected $casts = [
        'referral_codes' => 'array',
        'banners' => 'array'
    ];

    protected $attributes = [
        'referral_codes' => '[]',
        'banners' => '[]',
    ];

    public function scopeName($query, $name)
    { 
        if (trim($name ?: '') !== '') {
            $query->orWhere('name', 'like', trim($name) . '%');
        }
        
        return $query;
    }

    public function scopeUsername($query, $username)
    {
        if (trim($username ?: '') !== '') {
            $query->orWhere('username', 'like', trim($username) . '%');
        }
        
        return $query;
    }

    public function scopeReferralCode($query, string $referralCode)
    {
        $query->whereJsonContains('referral_codes', ['referral_code' => $referralCode]);

        return $query;
    }

    public function addReferralCode(array $data)
    {
        $referral_codes = $this->referral_codes;
        $referral_codes[] = $data;
        $this->referral_codes = $referral_codes;
    }

    public function addBanner(array $data)
    {
        if (!$this->ownsReferralCode($data['referral_code'])) {
          $this->addReferralCode([
            'active' => true,
            'referral_code' => $data['referral_code']
          ]);
        } 

        $banners = $this->banners;
        $banners[] = $data;
        $this->banners = $banners;
    }

    public function removeBanner(string $referralCode) 
    {
        $banners = $this->banners;
        foreach (array_keys($banners) as $key) {
            if (($banners[$key]['referral_code'] ?? null) == $referralCode) {
                unset($banners[$key]);
            }
            
            $banners = array_values($banners);
        }

        $this->banners = $banners;
    }

    public function ownsReferralCode(string $referralCode)
    {     
        return collect($this->referral_codes)
            ->filter(function ($refCode) use ($referralCode) {
                return $refCode['referral_code'] == $referralCode;
            })
            ->count() > 0
        ;
    }

    public static function checkDomain(string $website)
    {
        return DB::table('affiliates')
            ->select(
                'user_id',
                'banners.*'
            )
            ->crossJoin(
                DB::raw("json_table(banners, '$[*]' columns(domain varchar(255) path '$.domain')) as banners")
            )
            ->where('banners.domain', '=', $website)
            ->get()
        ;
    }

    public static function checkSocialMediaUsername(string $username)
    {
        return DB::table('affiliates')
            ->select(
                'user_id',
                'banners.*'
            )
            ->crossJoin(
                DB::raw("json_table(banners, '$[*]' columns(social_media_username varchar(255) path '$.social_media_username')) as banners")
            )
            ->where('banners.social_media_username', '=', $username)
            ->get()
        ;
    }
}
