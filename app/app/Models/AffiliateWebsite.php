<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class AffiliateWebsite extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'url', 'domain', 'social_media_username'];

    public static function byDomain(string $domain)
    {
        return DB::table('affiliate_websites')
            ->select(
                '*'
            )
            ->where('domain', '=', $domain)
            ->get()
        ;
    }

    public static function bySocialMediaUsername(string $username)
    {
        return DB::table('affiliate_websites')
            ->select(
                '*'
            )
            ->where('social_media_username', '=', $username)
            ->get()
        ;
    }
}
