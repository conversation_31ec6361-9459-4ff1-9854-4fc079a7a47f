<?php

use Pdp\Rules;
use Pdp\Domain;

if (! function_exists('get_domain')) {
  function get_domain(string $url) {
    $url = parse_url($url);
    $host = rtrim(isset($url['host']) ? $url['host'] : $url['path'], '/');
    $domain = Domain::fromIDNA2008($host);
    $data = Storage::get(env('UPLOAD_FOLDER') . '/' . 'public_suffix_list.dat');
    $publicSuffixList = Rules::fromString($data);
    $result = $publicSuffixList->resolve($domain);

    return $result->registrableDomain()->toString();
  }
}

if (! function_exists('get_social_media_username')) {
  function get_social_media_username(string $url) {
    $newUrl = parse_url($url);
    $username = ltrim($newUrl['path'], '/');

    return $newUrl['host']  === 'www.reddit.com' ? str_replace('user/', '', $username) : $username;
  }
}

if (! function_exists('is_social_media')) {
  function is_social_media(string $domain) {
    $socialMediaDomains = ['facebook.com', 'instagram.com', 'twitter.com', 't.me', 'wa.me', 'reddit.com'];

    return in_array($domain, $socialMediaDomains);
  }
}
