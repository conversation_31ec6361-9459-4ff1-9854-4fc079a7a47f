<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAffiliateRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'user_id' => 'required|unique:affiliates|numeric',
            'name' => 'required',
            'username' => 'required|unique:affiliates',
            'email' => 'required|unique:affiliates',
            'referral_code' => 'nullable|string',
            'website_url' => 'nullable|regex:/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/'
        ];
    }
}
