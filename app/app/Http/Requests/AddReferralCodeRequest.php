<?php

namespace App\Http\Requests;

use App\Models\Affiliate;
use Illuminate\Foundation\Http\FormRequest;

class AddReferralCodeRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'referral_code' => [
                'required',
                function($attribute, $value, $fail) {
                    if (Affiliate::referralCode($value)
                        ->get()
                        ->count() > 0
                    ) $fail('Referral Code is already been used.');
                }
            ],
            'active' => 'boolean'
        ];
    }
}
