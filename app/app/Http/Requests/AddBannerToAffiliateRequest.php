<?php

namespace App\Http\Requests;

use App\Models\Affiliate;
use App\Rules\ReferralCodeValid;
use App\Rules\WebsiteValid;
use Illuminate\Foundation\Http\FormRequest;

class AddBannerToAffiliateRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'website' => [
                'bail',
                'required', 
                'string', 
                'regex:/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/', 
                new WebsiteValid
            ],
            'type' => 'required|string',
            'language' => 'required|string',
            'size' => 'required|string',
            'campaign_name' => 'required|string',
            'referral_code' => [
                'bail',
                'required', 
                'string', 
                function($attribute, $value, $fail) {
                    $affiliate = Affiliate::referralCode($value)
                        ->get()
                        ->first();
                    $affiliateUserIdOnRequest = +request()->route()->parameter('userId');
                    
                    if ($affiliate !== null && $affiliate->user_id !== $affiliateUserIdOnRequest) $fail('Referral Code is invalid or has been already taken.');
                }
            ],
            'banner' => 'required|numeric'
        ];
    }
}
