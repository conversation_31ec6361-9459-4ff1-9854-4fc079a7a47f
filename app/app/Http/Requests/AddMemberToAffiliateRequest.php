<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddMemberToAffiliateRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'member_user_id' =>  ['numeric', 'unique:affiliate_member_mappings,member_user_id']
        ];
    }

    public function messages()
    {
        return [
            'member_user_id.unique' => 'This member is already linked to an affiliate.',
        ];
    }
}
