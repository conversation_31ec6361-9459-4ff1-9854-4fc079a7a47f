<?php

namespace App\Http\Requests;

use App\Models\Affiliate;
use Illuminate\Foundation\Http\FormRequest;

class LinkMemberRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'referral_code' => [
                'required', 
                function($attribute, $value, $fail) {
                    if (Affiliate::referralCode($value)
                        ->get()
                        ->count() === 0
                    ) $fail('Referral Code doesn\'t exist.');
                }
            ],
            'member_user_id' => ['required', 'numeric', 'unique:affiliate_member_mappings,member_user_id']
        ];
    }

    public function messages()
    {
        return [
            'member_user_id.unique' => 'This member is already linked to an affiliate.',
        ];
    }
}
