<?php

namespace App\Http\Controllers;

use App\BannerStorage;
use App\Http\Resources\BannerCollection;
use App\Http\Resources\BannerResource;
use App\Http\Requests\StoreBannerRequest;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BannerController extends Controller
{
    public function store(StoreBannerRequest $request, BannerStorage $storage)
    {
        $data = $request->validated();
        $banner = new Banner();
        $banner->fill($data);
        $banner->addToPaths($storage->upload($banner, $data['images']));
        $banner->save();

        return $banner;
    }

    public function update(Banner $banner, StoreBannerRequest $request, BannerStorage $storage)
    {
        $data = $request->validated();
        $banner->fill($data);
        $banner->addToPaths($storage->upload($banner, $data['images']));
        $banner->save();

        return $banner;
    }

    public function index(Request $request)
    {
        return Banner::name($request->get('name'))
            ->type($request->get('type'))
            ->language($request->get('language'))
            ->paginate($request->query('per_page', 10))
        ;
    }

    public function show(Banner $banner)
    {
        return $banner;
    }

    public function download(Banner $banner, Request $request, BannerStorage $storage)
    {
        $size = $request->query('size');
        $thumbnail = $request->query('thumbnail');

        return $storage->download($banner->paths[$size][$thumbnail ? 'thumbnail' : 'original']);
    }

    public function images(Banner $banner, BannerStorage $storage)
    {
        return $storage->downloadUrls($banner);
    }

    public function delete(Banner $banner)
    {
        $banner->delete();
    }

    public function deleteImage(Banner $banner, string $size)
    {
        $banner->removeFromPaths($size);
        $banner->save();
        return [];
    }
}
