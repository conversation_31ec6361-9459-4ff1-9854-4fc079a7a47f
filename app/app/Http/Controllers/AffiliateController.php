<?php

namespace App\Http\Controllers;

use App\Events\AffiliateMemberMappingSaved;
use App\Http\Requests\AddBannerToAffiliateRequest;
use App\Http\Requests\AddMemberToAffiliateRequest;
use App\Http\Requests\AddReferralCodeRequest;
use App\Http\Requests\LinkMemberRequest;
use App\Http\Requests\StoreAffiliateRequest;
use App\Http\Requests\UpdateAffiliateRequest;
use App\Models\Affiliate;
use App\Models\AffiliateMemberMapping;
use App\Models\AffiliateWebsite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class AffiliateController extends Controller
{
    public function index(Request $request)
    {
        return Affiliate::name($request->get('name'))
            ->username($request->get('name'))
            ->simplePaginate(20);
    }

    public function store(StoreAffiliateRequest $request)
    {
        $validated = array_filter($request->validated());
        $affiliate = new Affiliate();

        return $this->storeAffiliate($affiliate, $validated);
    }

    public function updateProfile(UpdateAffiliateRequest $request, int $affiliateUserId)
    {
        $validated = array_filter($request->validated());
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();

        return $this->storeAffiliate($affiliate, $validated);
    }

    private function storeAffiliate(Affiliate $affiliate, $validated): Affiliate
    {
        $affiliate->fill($validated);

        if (!empty($validated['referral_code'])) {
            $affiliate->addReferralCode([
                'active' => true,
                'referral_code' => $validated['referral_code']
            ]);
        }

        $affiliate->save();

        if (!empty($validated['website_url'])) {
            $domain = $this->getDomain($validated['website_url']);
            $socialMediaUsername = $this->getSocialMediaUsername($validated['website_url']);
            $affiliateWebsiteByUser = is_social_media($domain) ? AffiliateWebsite::bySocialMediaUsername($socialMediaUsername)->first() : AffiliateWebsite::byDomain($domain)->first();

            if ($affiliateWebsiteByUser === null) {
                $this->storeAffiliateWebsite([
                    'user_id' => $validated['user_id'],
                    'url' => $validated['website_url'],
                    'domain' => $domain,
                    'social_media_username' => $socialMediaUsername
                ]);
            }
        }

        return $affiliate;
    }

    public function storeAffiliateWebsite(array $data) 
    {
        $affiliateWebsite = new AffiliateWebsite();
        $affiliateWebsite->fill($data);
        $affiliateWebsite->save();
    }

    public function show(int $affiliateUserId)
    {
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();

        return $affiliate;
    }

    public function byReferralCode(string $referralCode)
    {
        $affiliates = Affiliate::referralCode($referralCode)->get();
       
        if ($affiliates->count() < 1) {
            abort(404, 'No affiliate found with referral code of ' . $referralCode);
        }

        return $affiliates->first()->toArray();
    }

    public function addReferralCode($affiliateUserId, AddReferralCodeRequest $request)
    {
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();
        $data = $request->validated();

        $affiliate->addReferralCode($data);
        $affiliate->save();

        return $affiliate;
    }

    public function listReferralCodes(int $affiliateUserId, Request $request)
    {
        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 10);
        
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();

        return collect($affiliate->referral_codes)
            ->reverse()
            ->values()
        ;
    }

    public function storeMember($affiliateUserId, AddMemberToAffiliateRequest $request)
    {
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();
        $data = $request->validated();

        $mapping = AffiliateMemberMapping::create([
            'member_user_id' => $data['member_user_id'],
            'affiliate_user_id' => $affiliate->user_id
        ]);

        return $mapping;
    }

    public function listMembers($affiliateUserId)
    {
        $mappings = AffiliateMemberMapping::where('affiliate_user_id', '=', $affiliateUserId);
   
        return $mappings->paginate(10);
    }
    
    public function removeMember($affiliateUserId, $memberUserId)
    {
        $mapping = AffiliateMemberMapping::where('affiliate_user_id', $affiliateUserId)
            ->where('member_user_id', $memberUserId)
        ;

        $mapping->delete();
    }

    public function linkMember(LinkMemberRequest $request)
    {
        $data = $request->validated();
        $affiliate = Affiliate::referralCode($data['referral_code'])
            ->get()
            ->first()
        ;

        $mapping = AffiliateMemberMapping::create(array_merge(
            ['affiliate_user_id' => $affiliate->user_id],
            $data
        ));

        return $mapping;
    }

    // TODO: Move to other microservice
    public function searchMembers(Request $request)
    {
        $keyword = $request->query('keyword', '');
        $searchResults = collect(
                DB::table('user')
                ->select('user_id','user_username', 'user_email', 'customer_full_name')
                ->join('customer', 'customer.customer_user_id', '=', 'user.user_id')
                ->where('customer_full_name', 'like', '%'. $keyword . '%')
                ->orWhere('user_email', 'like', '%'. $keyword . '%')
                ->limit(100)
                ->get()
                ->toArray()
            )
            ->map(function ($item) {
                return [
                    'text' => $item->customer_full_name . '(' . $item->user_email . ')', 'value' => $item->user_id,
                ];
            })
            ->toArray()
        ;
            
        return response()->json($searchResults);
    }


    public function memberAffiliate(int $memberUserId)
    {
        $mapping = AffiliateMemberMapping::where([
            ['member_user_id', '=', $memberUserId]
        ])->firstOrFail();

        return $mapping;
    }
    
    public function addBanner(int $affiliateUserId, AddBannerToAffiliateRequest $request)
    {
        $data = $request->validated();
        $domain = $this->getDomain($data['website']);
        $socialMediaUsername = $this->getSocialMediaUsername($data['website']);

        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();
        $affiliate->addBanner(array_merge(
            $data, 
            ['domain' => $domain, 'is_social_media' => is_social_media($domain), 'social_media_username' => $socialMediaUsername]
        ));
        $affiliate->save();

        $affiliateWebsiteByUser = is_social_media($domain) ? AffiliateWebsite::bySocialMediaUsername($socialMediaUsername)->first() : AffiliateWebsite::byDomain($domain)->first();

        if ($affiliateWebsiteByUser === null) {
            $this->storeAffiliateWebsite(['user_id' => $affiliateUserId, 'url' => $data['website'], 'domain' => $domain, 'social_media_username' => $socialMediaUsername]);
        }
    }

    private function getDomain($url)
    {
        return get_domain($url);
    }

    private function getSocialMediaUsername($url)
    {
        $domain = $this->getDomain($url);

        return is_social_media($domain) ? get_social_media_username($url) : '';
    }

    public function removeBanner(int $affiliateUserId, string $referralCode) 
    {
        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();
        $affiliate->removeBanner($referralCode);
        $affiliate->save();
    }

    public function listBanners(int $affiliateUserId, Request $request)
    {
        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 10);

        $affiliate = Affiliate::where('user_id', $affiliateUserId)->firstOrFail();
        $downloadUrl = "%s/banner/%d/download?size=%s";

        return collect($affiliate->banners)
            ->map(function ($data) use ($downloadUrl){
                $data['download_url'] = sprintf($downloadUrl, env('API_GATEWAY_URL'), $data['banner'], $data['size']);

                return $data;
            })
            ->reverse()
            ->values()
        ;
    }
}
