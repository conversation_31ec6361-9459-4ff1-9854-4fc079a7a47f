<?php

namespace App\Providers;

use App\BannerStorage;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->when(BannerStorage::class)
            ->needs('$uploadFolder')
            ->giveConfig('banner.upload_folder')
        ;

        $this->app->when(BannerStorage::class)
            ->needs('$apiGatewayUrl')
            ->give(env('API_GATEWAY_URL'))
        ;
     }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        
    }
}
