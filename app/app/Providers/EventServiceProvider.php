<?php

namespace App\Providers;

use App\Events\AffiliateMemberMappingSaved;
use App\EventListeners\AffiliateMappingListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AffiliateMemberMappingSaved::class => [
            AffiliateMappingListener::class,
        ],
    ];

    public function boot()
    {
    }
}
