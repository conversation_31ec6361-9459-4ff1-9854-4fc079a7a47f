<?php

namespace App\Rules;

use App\Models\Affiliate;
use App\Models\AffiliateWebsite;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Validation\Rule;

class WebsiteValid implements Rule
{
    private $invalidMessage;

    public function passes($attribute, $value)
    {
        $domain = get_domain($value);
        $affiliateUserId = +request()->route()->parameter('userId');
        
        if (is_social_media($domain)) {
            $socialMediaUsername = get_social_media_username($value);

            //To reuse deleted social media username by user
            $socialMediaUsernameByUser = AffiliateWebsite::bySocialMediaUsername($socialMediaUsername)->first();
            if ($socialMediaUsernameByUser !== null && $socialMediaUsernameByUser->user_id !== $affiliateUserId) return false;

            // $socialMediaBanner = Affiliate::checkSocialMediaUsername($socialMediaUsername)->first();
            // if ($socialMediaBanner !== null && $socialMediaBanner->user_id !== $affiliateUserId) return false;

            return true;
        }

        //To reuse deleted domain by user
        $domainByUser = AffiliateWebsite::byDomain($domain)->first(); 
        if ($domainByUser !== null && $domainByUser->user_id !== $affiliateUserId) return false;

        // $banner = Affiliate::checkDomain($domain)->first();
        // if ($banner !== null && $banner->user_id !== $affiliateUserId) return false;

        return true;
    }

    public function message()
    {
        return 'Website is invalid or has been already taken.';
    }
}
