<?php

namespace App;

use App\Models\Banner;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;
use Image;

// TODO: Delegate task in a queue when the need arises
class BannerStorage
{
    private $apiGatewayUrl;
    private $uploadFolder;

    public function __construct(string $uploadFolder, string $apiGatewayUrl)
    {
        $this->uploadFolder = $uploadFolder;
        $this->apiGatewayUrl = $apiGatewayUrl;
    }

    public function upload(Banner $banner, array $images): array
    {
        $paths = [];
        foreach ($images as $tmpImage) {
            $image = Image::make($tmpImage);
            $size = $image->width() . 'x' . $image->height();
            $fileName = sprintf(
                '%s-%s-%s-%s.%s',
                $size,
                $banner->type,
                $banner->language,
                time(),
                $tmpImage->getClientOriginalExtension()
            );

            $path = [
                'original' => $fileName,
                'thumbnail' =>  'thumb_' . $fileName
            ];

            Storage::put($this->uploadFolder . '/' . $path['original'], $image->stream()->__toString());
            $thumbnail = $image->resize(100,100);
            Storage::put($this->uploadFolder . '/' . $path['thumbnail'], $thumbnail->stream()->__toString());

            $paths[] = [ 'size' => $size, 'path' => $path ];
        }

        return $paths;
    }

    public function downloadUrls(Banner $banner): Collection
    {
        $downloadUrl = "%s/banner/%d/download?size=%s";
        $bannerId = $banner->id;

        return collect($banner->paths)
            ->keys()
            ->map(function ($size) use ($bannerId, $downloadUrl) {
                return [    
                    'size' => $size,
                    'download_url' => sprintf($downloadUrl, $this->apiGatewayUrl, $bannerId, $size),
                    'banner_id' => $bannerId
                ];
            })
            ->values()
        ;
    }

    public function download(string $path)
    {
        return Storage::download($this->uploadFolder . '/' . $path);
    }
}