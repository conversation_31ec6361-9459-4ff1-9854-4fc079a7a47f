<?php

namespace App\EventListeners;

use App\Events\AffiliateMemberMappingSaved;
use Illuminate\Support\Facades\DB;

/**
 * We have to set Customer's affiliate everytime a mapping is saved.
 * This is just to be consistent with BO implementation.
 * 
 * While, I can just retrieve the affiliate via the member id
 * the widget implementation makes it hard to do so.
 * I have to modify the select2 implementation which will
 * consume a lot of time.
 */
class AffiliateMappingListener
{
    public function __construct()
    {
    }

    public function handle(AffiliateMemberMappingSaved $event)
    {
        DB::table('customer')
            ->select('user_id', 'customer_id')
            ->join('user', 'customer.customer_user_id', '=', 'user.user_id')
            ->where('user_id', $event->mapping->member_user_id)
            ->update(['customer_affiliate_id' => $event->mapping->affiliate_user_id])
        ;
    }
}