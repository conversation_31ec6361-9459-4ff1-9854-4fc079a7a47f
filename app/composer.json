{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.5", "jeremykendall/php-domain-parser": "^6.3", "laravel/framework": "^11.0", "laravel/tinker": "^2.8.0", "league/flysystem-aws-s3-v3": "^3.1"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "phpunit/phpunit": "^10.0", "nunomaduro/collision": "^8.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan migrate --no-interaction -vvv --force", "@php artisan cache:clear", "@php artisan optimize", "@php artisan config:cache", "@php artisan route:cache", "@php artisan event:cache"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}