<?php

namespace Database\Seeders;

use App\Models\AffiliateWebsite;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MigrateAffiliateWebsiteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('affiliates')
            ->select('user_id', 'banners')
            ->get()
            ->each(function ($affiliate, $index) {
                $banners = json_decode($affiliate->banners, true);
                if (sizeof($banners) != 0) {
                    foreach ($banners as $banner) {
                        $socialMediaUsername = isset($banner['social_media_username']) ? $banner['social_media_username'] : '';
                        $socialMediaUsername = $banner['domain'] === 'reddit.com' ? str_replace('user/', '', $socialMediaUsername) : $socialMediaUsername;
                        $this->addAffiliateWebsite($affiliate->user_id, $banner['website'], $banner['domain'], $socialMediaUsername);
                    }
                }
            });
            
    }

    private function addAffiliateWebsite($affiliateUserId, $url, $domain, $socialMediaUsername) 
    {
        $affiliateWebsite = new AffiliateWebsite();
        $affiliateWebsite->fill(['user_id' => $affiliateUserId, 'url' => $url, 'domain' => $domain, 'social_media_username' => $socialMediaUsername]);
        $affiliateWebsite->save();
    }
}
