<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAffiliateMemberMappingsTable extends Migration
{
    public function up()
    {
	    if (!Schema::hasTable('affiliate_member_mappings'))
	    {
		    Schema::create('affiliate_member_mappings', function (Blueprint $table) {
			    $table->id();
			    $table->timestamps();
			    $table->integer('affiliate_user_id');
			    $table->integer('member_user_id');
			    $table->string('referral_code')->nullable();
		    });
	    }
    }

    public function down()
    {
        Schema::dropIfExists('affiliate_member_mappings');
    }
}
