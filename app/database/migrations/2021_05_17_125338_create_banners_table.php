<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBannersTable extends Migration
{
    public function up()
    {
	    if (!Schema::hasTable('banners'))
	    {
		    Schema::create('banners', function (Blueprint $table) {
			    $table->id();
			    $table->timestamps();
			    $table->string('name');
			    $table->string('type');
			    $table->string('language');
			    $table->json('paths');
		    });
	    }
    }

    public function down()
    {
        Schema::dropIfExists('banners');
    }
}
