<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAffiliateWebsitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('affiliate_websites')) {
            Schema::create('affiliate_websites', function (Blueprint $table) {
                $table->id();
                $table->timestamps();
                $table->integer('user_id');
                $table->string('url');
                $table->string('domain');
                $table->string('social_media_username');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('affiliate_websites');
    }
}
