<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAffiliatesTable extends Migration
{
    public function up()
    {
	    if (!Schema::hasTable('affiliates'))
	    {
		    Schema::create('affiliates', function (Blueprint $table) {
			    $table->id();
			    $table->timestamps();
			    $table->integer('user_id');
			    $table->string('email');
			    $table->string('username');
			    $table->string('name');
			    $table->json('referral_codes');
			    $table->json('banners');
		    });
	    }
    }

    public function down()
    {
        Schema::dropIfExists('affiliates');
    }
}
