FROM zimi/php:8.3-fpm

ENV PHP_OPCACHE_ENABLED="1"

RUN docker-php-ext-install pcntl \
  ;

#Copy codes to html folder
WORKDIR /var/www/html
COPY ./app .
COPY opt/php/*.ini $PHP_INI_DIR/conf.d/
COPY opt/php/www.conf $PHP_INI_DIR/../php-fpm.d/

#Install composer packages
RUN chmod -R 777 storage bootstrap \
  ; composer config --global use-github-api false \
  ; rm -rf vendor && mkdir -p vendor && php -d memory_limit=-1 `which composer` install -no --apcu-autoloader --no-scripts --no-progress --no-autoloader --no-cache \
  ; composer dumpautoload -no --apcu --no-interaction --no-scripts \
  ;

COPY ./opt/entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]

CMD ["php-fpm"]
