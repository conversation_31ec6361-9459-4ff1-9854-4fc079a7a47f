x-environment: &environment
  environment:
    - APP_NAME=Laravel
    - APP_ENV=local
    - APP_DEBUG=true
    - APP_KEY=base64:0xaXA7lZpisouboAauMGZB2pl/Xduj6KUXrhkp2a4RQ=
    - API_GATEWAY_URL=http://localhost:8001
    - AWS_SDK_VERSION=latest
    - AWS_DEFAULT_REGION=ap-northeast-1
    - AWS_ACCESS_KEY_ID=********************
    - AWS_SECRET_ACCESS_KEY=XUpLqXndyaHYP8vPFMvgN6FbFZOG4ubZPjbgT1Pi
    - AWS_S3_VERSION=2006-03-01
    - AWS_BUCKET=zmt-local
    - UPLOAD_FOLDER=local
    - BANNER_UPLOAD_FOLDER=local/uploads
    - DB_HOST=database
    - DB_DATABASE=backoffice_db
    - DB_USERNAME=backoffice
    - DB_PASSWORD=backoffice_pass
    - FILESYSTEM_DRIVER=s3
    - LOG_CHANNEL=daily
x-common: &common
  init: true
  networks:
    - piwinet
  restart: unless-stopped

services:
  affiliate-service:
    <<: *common
    build:
      context: .
      dockerfile: nginx.dockerfile
    image: affiliate-service
    depends_on:
      - affiliate-php
    environment:
      - FPM_SERVER=affiliate-php:9000

  affiliate-php:
    <<: [*environment, *common]
    build:
      context: .
    image: affiliate-php
    volumes:
      - ./app:/var/www/html:rw

  redis-affiliate:
    <<: *common
    image: redis:5-buster
    command: ["redis-server", "--appendonly", "yes"]

networks:
  piwinet:
    external: true
